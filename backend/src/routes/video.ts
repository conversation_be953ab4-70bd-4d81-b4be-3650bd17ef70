import { Router, Request, Response } from 'express';
import { Server } from 'socket.io';
import path from 'path';
import { 
  DownloadRequest, 
  VideoExtractResponse, 
  DownloadResponse,
  ApiResponse 
} from '../../../shared/types';
import { asyncHandler } from '../middleware/errorHandler';
import { videoExtractor } from '../services/videoExtractor';
import { downloadManager } from '../services/downloadManager';
import { fileManager } from '../utils/fileManager';
import { logger } from '../utils/logger';

const router = Router();

/**
 * @route POST /api/video/extract
 * @desc Extract video information from URL
 * @access Public
 */
router.post('/extract', asyncHandler(async (req: Request, res: Response) => {
  const { url } = req.body;

  if (!url) {
    return res.status(400).json({
      success: false,
      error: 'URL is required',
      code: 'MISSING_URL'
    });
  }

  logger.info('Video extraction requested', { url });

  const videoInfo = await videoExtractor.extractVideoInfo(url);

  const response: VideoExtractResponse = {
    success: true,
    data: videoInfo
  };

  return res.json(response);
}));

/**
 * @route POST /api/video/download
 * @desc Start video download
 * @access Public
 */
router.post('/download', asyncHandler(async (req: Request, res: Response) => {
  const downloadRequest: DownloadRequest = req.body;
  const io: Server = req.app.get('io');

  if (!downloadRequest.url) {
    return res.status(400).json({
      success: false,
      error: 'URL is required',
      code: 'MISSING_URL'
    });
  }

  logger.info('Download requested', downloadRequest);

  // Set up socket event listeners for this download
  downloadManager.on('downloadProgress', (progress) => {
    io.emit('downloadProgress', progress);
  });

  downloadManager.on('downloadComplete', (result) => {
    io.emit('downloadComplete', result);
  });

  downloadManager.on('downloadError', (error) => {
    io.emit('downloadError', error);
  });

  const downloadId = await downloadManager.startDownload(downloadRequest);

  return res.json({
    success: true,
    data: {
      downloadId,
      message: 'Download started'
    }
  });
}));

/**
 * @route GET /api/video/progress/:downloadId
 * @desc Get download progress
 * @access Public
 */
router.get('/progress/:downloadId', asyncHandler(async (req: Request, res: Response) => {
  const { downloadId } = req.params;

  const progress = downloadManager.getDownloadProgress(downloadId);

  if (!progress) {
    return res.status(404).json({
      success: false,
      error: 'Download not found',
      code: 'DOWNLOAD_NOT_FOUND'
    });
  }

  return res.json({
    success: true,
    data: progress
  });
}));

/**
 * @route DELETE /api/video/download/:downloadId
 * @desc Cancel download
 * @access Public
 */
router.delete('/download/:downloadId', asyncHandler(async (req: Request, res: Response) => {
  const { downloadId } = req.params;

  const cancelled = await downloadManager.cancelDownload(downloadId);

  if (!cancelled) {
    return res.status(404).json({
      success: false,
      error: 'Download not found or already completed',
      code: 'DOWNLOAD_NOT_FOUND'
    });
  }

  return res.json({
    success: true,
    message: 'Download cancelled'
  });
}));

/**
 * @route GET /api/video/downloads
 * @desc Get all active downloads
 * @access Public
 */
router.get('/downloads', asyncHandler(async (req: Request, res: Response) => {
  const activeDownloads = downloadManager.getAllActiveDownloads();

  return res.json({
    success: true,
    data: activeDownloads
  });
}));

/**
 * @route GET /api/video/download/:filename
 * @desc Download file
 * @access Public
 */
router.get('/download/:filename', asyncHandler(async (req: Request, res: Response) => {
  const { filename } = req.params;

  // Security check - prevent directory traversal
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return res.status(400).json({
      success: false,
      error: 'Invalid filename',
      code: 'INVALID_FILENAME'
    });
  }

  const filePath = fileManager.getDownloadPath(filename);
  const fileExists = await fileManager.fileExists(filename);

  if (!fileExists) {
    return res.status(404).json({
      success: false,
      error: 'File not found',
      code: 'FILE_NOT_FOUND'
    });
  }

  logger.info('File download requested', { filename });

  // Set appropriate headers
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  // Stream the file
  res.sendFile(filePath, (err) => {
    if (err) {
      logger.error('File download error', { filename, error: err });
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: 'File download failed',
          code: 'DOWNLOAD_FAILED'
        });
      }
    } else {
      logger.info('File downloaded successfully', { filename });
    }
  });
}));

/**
 * @route GET /api/video/formats
 * @desc Get available video formats for URL
 * @access Public
 */
router.post('/formats', asyncHandler(async (req: Request, res: Response) => {
  const { url } = req.body;

  if (!url) {
    return res.status(400).json({
      success: false,
      error: 'URL is required',
      code: 'MISSING_URL'
    });
  }

  logger.info('Formats requested', { url });

  const formats = await videoExtractor.getAvailableFormats(url);

  return res.json({
    success: true,
    data: formats
  });
}));

export default router;
